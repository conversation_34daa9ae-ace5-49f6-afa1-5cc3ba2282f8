# Google Apps Script Structured Logging Library

## 1. Introduction & Benefits

This document provides instructions for using the centralized logging library for Google Apps Script. The library enables you to send structured, reliable logs directly from your Apps Script projects to the Google Cloud Logging service.

Using this shared library provides several key advantages over native methods like `Logger.log()` or `console.log()`:

*   **True Severity Levels:** Logs appear in Google Cloud with their actual severity (`DEBUG`, `INFO`, `WARNING`, `ERROR`, etc.), allowing for proper filtering and alerting. `Logger.log` records everything as `INFO`.
*   **Rich, Structured Payloads:** Log entries are automatically enriched with a `jsonPayload` containing the calling function's name, the user's email, and any custom data you provide. This makes logs highly searchable and useful for debugging.
*   **Correct Library Context:** The factory pattern (`Logger.getInstance()`) ensures the library always uses the correct script's properties, making it safe for use by multiple users and in different projects.
*   **Centralized & Consistent:** Ensures all projects adhere to the same high standard for logging, making logs consistent and easily queryable in one place.
*   **Built-in Validation:** The library includes a setup function that verifies the configuration, preventing silent failures and ensuring your setup is correct from the start.
*   **Convenient Log Access:** Provides helper functions to get a direct, clickable URL to the logs for your specific script or the entire GCP project.

---

## 2. Core Functionality

The library exposes a `Logger` object that acts as a factory to create a configured `Log` instance.

### Factory Function

*   `Logger.getInstance(scriptProperties)`: Returns a `Log` object configured for the script that is calling it. You must pass it the script's properties, e.g., `PropertiesService.getScriptProperties()`.

### Setup Function

*   `Logger.setup(gcpProjectId, scriptProperties)`: The one-time configuration function. You must pass it your GCP Project ID and the script's properties.

### `Log` Object Methods

The `Log` object returned by `getInstance` contains the following methods:

*   `Log.debug(message, data)`
*   `Log.info(message, data)`
*   `Log.notice(message, data)`
*   `Log.warn(message, data)`
*   `Log.error(message, data)`
*   `Log.critical(message, data)`
*   `Log.alert(message, data)`
*   `Log.emergency(message, data)`
*   `Log.getLogsUrl()`: Returns a direct link to the logs for the current script.
*   `Log.getProjectLogsUrl()`: Returns a link to all logs for the configured GCP project.

---

## 3. How to Get Started

Follow these steps to integrate the logging library into your Google Apps Script project.

### Step 1: Add the Library to Your Project

1.  In your Apps Script project, click the **+** icon next to "Libraries".
2.  In the "Script ID" field, paste the following ID:
    > `1a2pKpnH20246miqgwW3F1SYSaxxQBnXPzSIxf0tdmWd1bWIzQ5-WHD6u`
3.  Click **Look up**.
4.  Choose the latest version of the library.
5.  **Crucially, change the "Identifier" to `Logger`**. This allows you to use the `Logger.getInstance()` factory pattern.
6.  Click **Add**.

### Step 2: Link Project and Enable APIs

Your Apps Script project must be linked to a Google Cloud Project with the correct APIs enabled.

1.  **Link to GCP Project:** In your Apps Script project settings, link the script to the target Google Cloud Project.
2.  **Enable Cloud Logging API:** In your linked Google Cloud Project, ensure the **"Cloud Logging API"** is enabled.
3.  **Set OAuth Scopes:** In your script's `appsscript.json` manifest file, add the following `oauthScopes`:
    ```json
    "oauthScopes": [
      "https://www.googleapis.com/auth/logging.write",
      "https://www.googleapis.com/auth/userinfo.email",
      "https://www.googleapis.com/auth/script.external_request"
    ]
    ```

### Step 3: Configure the Library (One-Time Setup)

Before you can log, you must run a setup function once to tell the library which GCP project to send logs to.

1.  Create a new function in your script.
2.  Call the `Logger.setup()` function, passing your GCP Project ID and the script's properties.
3.  Run this function once from the Apps Script editor.

```javascript
// This is a one-time setup function. Run it once from the editor.
function configureMyLogger() {
  // Replace with your actual GCP Project ID.
  const gcpProjectId = 'my-gcp-project-id-12345';
  
  Logger.setup(gcpProjectId, PropertiesService.getScriptProperties());
}
```

After running it, check the execution logs. You should see a success message and a clickable link to your new log stream in the Google Cloud Console.

### Step 4: Use the Logger in Your Code

Once the one-time setup is complete, you must get a configured `Log` instance in your functions before you can send logs.

```javascript
function processUserData() {
  // Best Practice: Get the logger instance at the start of your function.
  const Log = Logger.getInstance(PropertiesService.getScriptProperties());

  try {
    Log.debug('Starting user data processing.', { userId: 'user-abc-123' });
    
    // ... some operation ...

    Log.info('User data processed successfully.', { recordsProcessed: 150 });

  } catch (e) {
    // The error object is automatically handled.
    Log.error('Data processing failed.', { error: e.message, stack: e.stack });
  }
  
  // You can also get a direct link to the logs anytime.
  console.log('View logs for this script at:', Log.getLogsUrl());
}
