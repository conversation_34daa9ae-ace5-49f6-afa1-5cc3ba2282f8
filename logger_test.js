/**
 * @fileoverview Test functions for the Cloud Logging library.
 * This file is for development and verification purposes.
 */

// --- Test Setup ---
// This mock allows the test to run in a project where the logging functions
// are defined locally, simulating how they would be called from a consumer
// project that has imported this script as a library with the identifier "Logger".
const Logger = {
  getInstance: getInstance,
  setup: setup
};
// --- End Test Setup ---

/**
 * IMPORTANT: ONE-TIME SETUP
 *
 * Run this function once to configure the logger with your Google Cloud Project ID.
 * Replace 'YOUR_GCP_PROJECT_ID_HERE' with your actual GCP Project ID.
 */
function runFirst_configureLogger() {
  // This function must be run once before runLoggerTests() will work.
  // It stores your GCP Project ID in Script Properties for the logger to use.
  try {
    Logger.setup('YOUR_GCP_PROJECT_ID_HERE', PropertiesService.getScriptProperties());
  } catch (e) {
    console.error(`Configuration failed. Please ensure you have replaced the placeholder project ID and that the Cloud Logging API is enabled. Error: ${e.message}`);
  }
}


/**
 * Main test function to verify all logging levels and helper functions.
 *
 * To run this test:
 * 1. Run `runFirst_configureLogger()` once to set up your GCP Project ID.
 * 2. Select "runLoggerTests" from the function dropdown in the Apps Script Editor.
 * 3. Click "Run".
 * 4. View the output in Google Cloud Logging for your configured project.
 *    A direct link to the logs will be printed in the Apps Script execution log.
 */
function runLoggerTests() {
  console.log('--- Starting Logger Tests ---');

  // Get a configured logger instance.
  const Log = Logger.getInstance(PropertiesService.getScriptProperties());

  // Test each severity level
  Log.debug('This is a debug message.', { details: 'Verbose details for developers.' });
  Log.info('Script execution started.');
  Log.notice('A user has just registered.', { userId: 'user-xyz-789' });
  Log.warn('API endpoint is deprecated and will be removed soon.', { endpoint: '/api/v1/data' });

  try {
    throw new Error('Failed to connect to external service.');
  } catch (e) {
    Log.error(e.message, { stack: e.stack, component: 'ExternalApiService' });
  }

  Log.critical('Database connection lost. Application cannot proceed.', { db_host: 'db.example.com' });
  Log.alert('A security vulnerability has been detected.', { vulnerability: 'SQL-Injection Attempt' });
  Log.emergency('System is unstable and requires immediate human intervention!', { system: 'core-backend' });

  // Test with different data payloads
  Log.info('User query finished.', { userCount: 50, queryTime: 1200 });
  Log.info('Testing with null data payload.', null);
  Log.warn('Testing with undefined data payload.', undefined);
  Log.error('Testing with a primitive value as data payload.', 'This is a string, not an object');


  // Test helper functions
  const scriptLogsUrl = Log.getLogsUrl();
  const projectLogsUrl = Log.getProjectLogsUrl();

  console.log(`Direct URL to this script's logs: ${scriptLogsUrl}`);
  console.log(`URL to all logs in the project: ${projectLogsUrl}`);
  Log.info('Successfully retrieved log URLs.', { scriptUrl: scriptLogsUrl, projectUrl: projectLogsUrl });

  console.log('--- Logger Tests Finished ---');
}
