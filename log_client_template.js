/**
 * @fileoverview A template for client scripts using the shared Cloud Logging library.
 *
 * To use this template:
 * 1. Copy this file into your Google Apps Script project.
 * 2. Follow the setup instructions below.
 */

// =================================================================================
// --- STEP 1: ADD THE LIBRARY ---
// =================================================================================
// 1. In the Apps Script Editor, click the "+" icon next to "Libraries".
// 2. In the "Script ID" field, paste the following ID:
//    1a2pKpnH20246miqgwW3F1SYSaxxQBnXPzSIxf0tdmWd1bWIzQ5-WHD6u
// 3. Click "Look up".
// 4. IMPORTANT: Change the "Identifier" to "Logger".
// 5. Select the latest version and click "Add".
// =================================================================================


// =================================================================================
// --- STEP 2: CONFIGURE THE LOGGER (RUN ONCE) ---
// =================================================================================
/**
 * A one-time setup function to configure the logging library.
 *
 * To run this:
 * 1. Replace 'YOUR_GCP_PROJECT_ID_HERE' with your actual Google Cloud Project ID.
 * 2. Select "setupLogger" from the function dropdown in the Apps Script Editor.
 * 3. Click "Run".
 * 4. Authorize the script when prompted.
 * 5. Check the execution logs for a success message.
 */
function setupLogger() {
  try {
    // --- REPLACE THIS WITH YOUR GCP PROJECT ID ---
    const gcpProjectId = 'YOUR_GCP_PROJECT_ID_HERE';
    // -----------------------------------------

    Logger.setup(gcpProjectId, PropertiesService.getScriptProperties());
  } catch (e) {
    // This will catch configuration errors and display them prominently.
    console.error(`FATAL: Logger configuration failed. Please check the GCP Project ID and ensure the Cloud Logging API is enabled. Error: ${e.message}`);
    // Optionally, show an alert to the user if this is part of an interactive workflow.
    // SpreadsheetApp.getUi().alert(`Logger configuration failed: ${e.message}`);
  }
}
// =================================================================================


// =================================================================================
// --- STEP 3: INITIALIZE THE LOGGER ---
// =================================================================================
// To use the logger, uncomment the following line.
// This initializes the logger as a global variable, which is a safe pattern
// for web apps that are deployed to "execute as the user accessing the web app".

// const Log = Logger.getInstance(PropertiesService.getScriptProperties());
// =================================================================================


// =================================================================================
// --- STEP 4: EXAMPLE USAGE ---
// =================================================================================
/**
 * An example function demonstrating how to use the logger.
 */
function doSomethingUseful() {
  try {
    Log.info('Starting the process.');

    // --- Your script's logic goes here ---
    const data = { item: 'Example', value: 123 };
    Log.debug('Processing data.', data);
    // ------------------------------------

    Log.info('Process completed successfully.', { itemsProcessed: 1 });

    // You can also get a direct link to this script's logs.
    console.log(`View logs for this script at: ${Log.getLogsUrl()}`);

  } catch (e) {
    // Best practice for error handling: log the error with its details.
    Log.error('An unexpected error occurred.', { error: e.message, stack: e.stack });

    // Re-throw the error if you want the script execution to halt and be marked as "Failed".
    // throw e;
  }
}
// =================================================================================
