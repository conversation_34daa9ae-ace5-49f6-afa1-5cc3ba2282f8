# Refactoring Plan: Solving the Apps Script Library Context Issue

This document outlines the problem with the current logging library's configuration handling and proposes a robust solution to ensure it works correctly and securely for multiple users and projects.

## The Problem: Incorrect Property Storage Context

When a Google Apps Script project is used as a library, it runs in its own context. The core issue is that the library currently uses `PropertiesService.getScriptProperties()` to save and load its configuration (like the GCP Project ID).

This means the configuration is stored with the *library's script*, not the *script that is using the library*.

**Consequence:** When a consuming script calls a function like `Log.info()`, the library tries to read the configuration from its own properties, finds nothing, and throws a "Logger not configured" error.

### Example of the Problem

**Current Library `setup` (Simplified):**
```javascript
// In the Library's Code.js
function setup(gcpProjectId) {
  // This saves the ID to the LIBRARY's properties, not the calling script's.
  const scriptProperties = PropertiesService.getScriptProperties();
  scriptProperties.setProperty('GCP_PROJECT_ID', gcpProjectId);
}
```

**Current Consuming Script Usage:**
```javascript
// In the consuming script
function configure() {
  // This call correctly saves the project ID, but in the wrong place (the library's properties).
  Log.setup('my-project-123');
}

function doWork() {
  // This call will FAIL because the library can't find the property.
  Log.info('Starting work...');
}
```

---

## Initial (Flawed) Solution: Using `UserProperties`

A common first thought is to use `PropertiesService.getUserProperties()`. These properties are shared across all scripts for a single user.

**Flaw:** This approach would require **every individual user** of the consuming script to run the `setup` function once. If a script is shared with 20 people, all 20 would need to manually run the configuration step, which is not a scalable or user-friendly solution.

---

## Recommended Solution: The Factory Pattern (Passing Context Explicitly)

The most robust and architecturally sound solution is to refactor the library to follow a "factory" pattern. Instead of the library implicitly trying to find its configuration, the consuming script will explicitly provide its context (its own `ScriptProperties`).

The library will provide a factory function, `Log.getInstance()`, which will create and return a configured "logger" object.

### How it Works

1.  **`Log.getInstance(properties)`:** A new function in the library that accepts a `Properties` object from the calling script.
2.  **Return a Logger Object:** This function returns an object containing all the logging methods (`info`, `warn`, `debug`, etc.). This object holds the configuration internally.
3.  **Explicit Context:** The calling script is now responsible for passing its own properties, ensuring the context is never wrong.

### Example of the Proposed Solution

**Proposed Library `getInstance` (Simplified):**
```javascript
// In the Library's Code.js
function getInstance(scriptProperties) {
  const config = {
    projectId: scriptProperties.getProperty('GCP_PROJECT_ID'),
    logId: scriptProperties.getProperty('LOG_ID')
  };

  // Return an object with all the logging functions
  return {
    info: function(message, data) {
      // 'config' is now correctly scoped and available here
      logToCloudLogging(config.projectId, config.logId, 'INFO', message, data);
    },
    warn: function(message, data) {
      logToCloudLogging(config.projectId, config.logId, 'WARNING', message, data);
    },
    // ... and so on for debug, error, etc.
  };
}

// The setup function would also be modified to accept properties
function setup(gcpProjectId, scriptProperties) {
  scriptProperties.setProperty('GCP_PROJECT_ID', gcpProjectId);
  // ...
}
```

**Proposed Consuming Script Usage:**
```javascript
// In the consuming script

// Get the script's own properties once.
const SCRIPT_PROPERTIES = PropertiesService.getScriptProperties();

// Create a configured logger instance by passing the properties.
const logger = Log.getInstance(SCRIPT_PROPERTIES);

function configure() {
  // Pass the properties to the setup function.
  Log.setup('my-project-123', SCRIPT_PROPERTIES);
}

function doWork() {
  // This call will SUCCEED because the 'logger' object has the correct configuration.
  logger.info('Starting work...');
  logger.warn('Something might be wrong!');
}
```

### Advantages of this Approach

*   **Correct Context:** The configuration is correctly stored and read from the consuming script's properties.
*   **Multi-User Safe:** It works for all users of a script without any extra steps, as all users of a project share the same `ScriptProperties`.
*   **Stateless & Robust:** The library itself remains stateless, making it more predictable and easier to maintain. This is the standard best practice for solving this common issue in Apps Script.
