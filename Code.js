/**
 * @fileoverview A centralized logging library for Google Apps Script.
 *
 * This script provides a factory function, `getInstance()`, to create a configured
 * logger object that sends structured logs to the Google Cloud Logging API.
 * It is intended to be deployed and used as a shared library.
 *
 * --- INSTRUCTIONS FOR USE AS A LIBRARY ---
 *
 * 1. DEPLOY THE LIBRARY:
 *    - In the Apps Script editor for this project, go to "Deploy" > "New deployment".
 *    - Select "Library" from the "Select type" gear icon.
 *    - Give it a description (e.g., "Cloud Logging Library v5").
 *    - Click "Deploy".
 *    - Copy the "Script ID" from the deployment details.
 *
 * 2. ADD THE LIBRARY TO YOUR PROJECT:
 *    - Open the Apps Script project where you want to use the logger.
 *    - Click the "+" icon next to "Libraries".
 *    - Paste the "Script ID" you copied and click "Look up".
 *    - **IMPORTANT:** Change the "Identifier" to "Logger". This is the recommended
 *      identifier for clean and consistent syntax.
 *    - Click "Add".
 *
 * 3. ENABLE REQUIRED APIS & SCOPES:
 *    - In your consuming script's `appsscript.json`, ensure the necessary OAuth scopes are present:
 *      "oauthScopes": [
 *        "https://www.googleapis.com/auth/logging.write",
 *        "https://www.googleapis.com/auth/userinfo.email",
 *        "https://www.googleapis.com/auth/script.external_request"
 *      ]
 *    - Link your Apps Script project to the target Google Cloud Project.
 *    - Ensure the "Cloud Logging API" is enabled in that Google Cloud Project.
 *
 * 4. CONFIGURE THE LIBRARY (ONE-TIME SETUP):
 *    - Before using the logger, you must run its setup function from your script.
 *    - This function configures the library with your specific GCP Project ID and
 *      validates the configuration by sending a test log.
 *
 *      function configureMyLogger() {
 *        // Replace with your actual GCP Project ID.
 *        Logger.setup('my-gcp-project-id-12345', PropertiesService.getScriptProperties());
 *      }
 *
 * 5. USE THE LOGGER IN YOUR CODE:
 *    - In your functions, get a configured logger instance.
 *
 *      function doSomething() {
 *        const Log = Logger.getInstance(PropertiesService.getScriptProperties());
 *
 *        Log.debug('Starting user data processing.', { userId: '123' });
 *        Log.info('User logged in successfully.');
 *        Log.warn('API response time is slow.', { responseTime: 2500 });
 *
 *        const url = Log.getLogsUrl();
 *        console.log('View this script\'s logs at:', url);
 *      }
 */

// --- Public Factory and Setup Functions ---

/**
 * Creates and returns a configured logger instance.
 * @param {GoogleAppsScript.Properties.Properties} scriptProperties The Properties object from the consuming script (e.g., PropertiesService.getScriptProperties()).
 * @returns {object} A logger object with logging and helper methods.
 */
function getInstance(scriptProperties) {
  const config = _getConfigFromProperties(scriptProperties);

  const getCallerFunctionName = () => {
    try {
      throw new Error();
    } catch (e) {
      const callerLine = e.stack.split('\n')[3].trim();
      const match = callerLine.match(/at (\S+)/);
      return match ? match[1] : 'unknown_function';
    }
  };

  const logger = {};
  const severities = ['DEBUG', 'INFO', 'NOTICE', 'WARNING', 'ERROR', 'CRITICAL', 'ALERT', 'EMERGENCY'];
  severities.forEach(severity => {
    logger[severity.toLowerCase()] = function(message, data) {
      logToCloudLogging(config.projectId, config.logId, severity, message, data, getCallerFunctionName());
    };
  });

  // Remap 'warn' to 'warning' for convenience
  logger.warn = logger.warning;

  logger.getLogsUrl = function() {
    return _buildLogsUrl(config.projectId, config.logId);
  };

  logger.getProjectLogsUrl = function() {
    return `https://console.cloud.google.com/logs/query?project=${config.projectId}`;
  };

  return logger;
}

/**
 * Configures and validates the logging library for the consuming script.
 * This function should be run once before any logging calls are made.
 * @param {string} gcpProjectId The Google Cloud Project ID to send logs to.
 * @param {GoogleAppsScript.Properties.Properties} scriptProperties The Properties object from the consuming script.
 */
function setup(gcpProjectId, scriptProperties) {
  if (!gcpProjectId || typeof gcpProjectId !== 'string' || gcpProjectId.trim() === '' || gcpProjectId.includes('YOUR_GCP_PROJECT_ID_HERE')) {
    throw new Error('Invalid or placeholder GCP Project ID provided. Please provide a valid string.');
  }
  if (!scriptProperties) {
    throw new Error('The scriptProperties object must be provided to the setup function.');
  }

  const logId = ScriptApp.getScriptId();

  try {
    _validateSetup(gcpProjectId, logId);
  } catch (e) {
    throw new Error(
      `Cloud Logging setup validation failed for project "${gcpProjectId}". ` +
      'Please check that: 1) The GCP Project ID is correct. ' +
      '2) The Cloud Logging API is enabled in that project. ' +
      '3) The script has the required OAuth scopes in appsscript.json. ' +
      `Original error: ${e.message}`
    );
  }

  scriptProperties.setProperty('GCP_PROJECT_ID', gcpProjectId);
  scriptProperties.setProperty('LOG_ID', logId);

  const logsUrl = _buildLogsUrl(gcpProjectId, logId);

  console.log(`Logger configured and validated successfully for Project ID: ${gcpProjectId}`);
  console.log(`View logs at: ${logsUrl}`);
}


// --- Private Helper Functions ---

/**
 * Retrieves the configured GCP Project ID and Log ID from the provided Properties object.
 * @param {GoogleAppsScript.Properties.Properties} scriptProperties The properties object to read from.
 * @returns {{projectId: string, logId: string}} The configuration object.
 * @private
 */
function _getConfigFromProperties(scriptProperties) {
  if (!scriptProperties) {
    throw new Error(
      'scriptProperties not provided. The logger must be initialized with a properties object, e.g., Logger.getInstance(PropertiesService.getScriptProperties()).'
    );
  }
  const projectId = scriptProperties.getProperty('GCP_PROJECT_ID');
  const logId = scriptProperties.getProperty('LOG_ID');

  if (!projectId || !logId) {
    throw new Error(
      'Logger not configured. Please run the `Logger.setup("YOUR_GCP_PROJECT_ID", PropertiesService.getScriptProperties())` function once to configure the library.'
    );
  }

  return { projectId, logId };
}

/**
 * Builds a URL to a specific log stream in the Google Cloud Console.
 * @param {string} projectId The GCP Project ID.
 * @param {string} logId The Log ID.
 * @returns {string} The fully constructed URL.
 * @private
 */
function _buildLogsUrl(projectId, logId) {
  const logName = _getLogName(projectId, logId);
  const encodedLogName = encodeURIComponent(logName);
  return `https://console.cloud.google.com/logs/query;query=logName%3D%22${encodedLogName}%22?project=${projectId}`;
}

/**
 * Constructs the standardized log name for Cloud Logging.
 * @param {string} projectId The GCP Project ID.
 * @param {string} logId The Log ID (typically the Script ID).
 * @returns {string} The fully-qualified log name.
 * @private
 */
function _getLogName(projectId, logId) {
  return `projects/${projectId}/logs/${logId}`;
}

/**
 * A private helper to write a single test log entry to validate setup.
 * @param {string} projectId The project ID to test.
 * @param {string} logId The log ID to test.
 * @private
 */
function _validateSetup(projectId, logId) {
  const validationApiEndpoint = 'https://logging.googleapis.com/v2/entries:write';
  const commonOptions = {
    method: 'post',
    contentType: 'application/json',
    headers: { 'Authorization': 'Bearer ' + ScriptApp.getOAuthToken() }
  };

  const genericLogName = `projects/${projectId}/logs/validation-test`;
  const genericEntry = {
    logName: genericLogName,
    resource: { type: "app_script_function", labels: { function_name: "Logger.setup_validation" } },
    severity: 'INFO',
    jsonPayload: { message: 'Validating project and API access.' }
  };
  const genericOptions = Object.assign({}, commonOptions, { payload: JSON.stringify({ entries: [genericEntry] }) });

  try {
    UrlFetchApp.fetch(validationApiEndpoint, genericOptions);
  } catch (e) {
    throw new Error(`Initial validation failed. This likely means the GCP Project ID "${projectId}" is incorrect or the Cloud Logging API is not enabled. Original error: ${e.message}`);
  }

  const specificLogName = _getLogName(projectId, logId);
  const specificEntry = {
    logName: specificLogName,
    resource: { type: "app_script_function", labels: { function_name: "Logger.setup" } },
    severity: 'INFO',
    jsonPayload: { message: 'Logging library setup and validation successful.' }
  };
  const specificOptions = Object.assign({}, commonOptions, { payload: JSON.stringify({ entries: [specificEntry] }) });

  try {
    UrlFetchApp.fetch(validationApiEndpoint, specificOptions);
  } catch (e) {
    throw new Error(`Project validation succeeded, but writing to the script-specific log failed. This may indicate an issue with the script ID format. Log name used: "${specificLogName}". Original error: ${e.message}`);
  }
}

// --- Private Core Logging Function ---

/**
 * Logs a structured message to Google Cloud Logging.
 * @param {string} projectId The Google Cloud Project ID.
 * @param {string} logId A custom identifier for your log stream.
 * @param {string} severity The desired log severity.
 * @param {string} message The primary log message.
 * @param {Object} [data={}] Optional. An object containing additional structured data.
 * @param {string} callerName The name of the function that initiated the log.
 * @private
 */
function logToCloudLogging(projectId, logId, severity, message, data = {}, callerName) {
  const LOGGING_API_ENDPOINT = `https://logging.googleapis.com/v2/entries:write`;
  const userEmail = Session.getActiveUser().getEmail();

  const logEntry = {
    logName: _getLogName(projectId, logId),
    resource: {
      type: "app_script_function",
      labels: {
        project_id: projectId,
        function_name: callerName,
        invocation_type: 'manual'
      }
    },
    severity: severity,
    jsonPayload: {
      message: message,
      data: data,
      user: userEmail,
      functionName: callerName
    }
  };

  const options = {
    method: 'post',
    contentType: 'application/json',
    payload: JSON.stringify({ entries: [logEntry] }),
    muteHttpExceptions: true,
    headers: {
      'Authorization': 'Bearer ' + ScriptApp.getOAuthToken()
    }
  };

  try {
    const response = UrlFetchApp.fetch(LOGGING_API_ENDPOINT, options);
    const responseCode = response.getResponseCode();
    if (responseCode < 200 || responseCode >= 300) {
      console.error(`Failed to send log to Cloud Logging. Status: ${responseCode}, Response: ${response.getContentText()}`);
    }
  } catch (e) {
    console.error(`Error during UrlFetchApp call to Cloud Logging API: ${e.message}`);
  }
}
